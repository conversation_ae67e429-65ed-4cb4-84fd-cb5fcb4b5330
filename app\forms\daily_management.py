from flask_wtf import FlaskForm
from wtforms import SelectField, DateField, IntegerField, TextAreaField, FileField
from wtforms.validators import DataRequired, Optional, FileRequired, FileAllowed

class InspectionPhotoForm(FlaskForm):
    """检查照片表单"""
    inspection_type = SelectField('检查类型', validators=[DataRequired()], choices=[
        ('', '请选择检查类型'),
        ('morning', '早晨检查'),
        ('noon', '中午检查'),
        ('evening', '晚上检查')
    ])
    inspection_date = DateField('检查日期', validators=[DataRequired()])
    rating = IntegerField('评分', validators=[Optional()], default=0)
    description = TextAreaField('备注说明', validators=[Optional()])
    photo = FileField('照片', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png'], '只允许上传jpg、jpeg、png格式的图片')
    ]) 