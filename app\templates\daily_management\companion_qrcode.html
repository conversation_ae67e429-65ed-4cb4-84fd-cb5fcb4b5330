{% extends 'base.html' %}

{% block title %}陪餐记录二维码{% endblock %}

{% block styles %}
<style>
    .qrcode-card {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
    }

    .qrcode-card .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        font-weight: bold;
        padding: 1rem 1.25rem;
    }

    .qrcode-card .card-body {
        padding: 1.5rem;
        text-align: center;
    }

    .qrcode-image {
        max-width: 250px;
        margin: 0 auto 1.5rem auto;
    }

    .qrcode-description {
        margin-bottom: 1.5rem;
    }

    .qrcode-url {
        word-break: break-all;
        background-color: #f8f9fc;
        padding: 0.75rem;
        border-radius: 0.35rem;
        font-family: monospace;
        margin-bottom: 1.5rem;
    }

    .qrcode-actions {
        margin-top: 1.5rem;
    }

    .qrcode-actions .btn {
        margin: 0 0.5rem;
    }

    .platform-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4e73df;
        margin-bottom: 1rem;
        text-align: center;
    }

    .print-section {
        display: none;
    }

    @media print {
        .print-section {
            display: block;
        }

        .no-print {
            display: none;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
        }

        .qrcode-card {
            box-shadow: none;
            border: 1px solid #ddd;
        }

        .qrcode-card .card-header {
            background: #f8f9fc;
            color: #000;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">陪餐记录二维码</h1>
        <div>
            <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> 返回陪餐记录
            </a>
            <button class="btn btn-primary btn-sm" onclick="window.print()">
                <i class="fas fa-print fa-sm text-white-50"></i> 打印二维码
            </button>
        </div>
    </div>

    <!-- 打印时显示的标题 -->
    <div class="print-section">
        <div class="platform-title">校园餐智慧食堂监管平台</div>
        <h2 class="text-center mb-4">{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }} 陪餐记录二维码</h2>
    </div>

    <div class="row">
        <!-- 上传照片二维码 -->
        <div class="col-lg-6">
            <div class="qrcode-card">
                <div class="card-header">
                    <i class="fas fa-camera mr-2"></i> 上传陪餐记录照片
                </div>
                <div class="card-body">
                    <img src="data:image/png;base64,{{ qrcode_base64 }}" alt="上传照片二维码" class="qrcode-image">
                    <div class="qrcode-description">
                        <p>扫描上方二维码，上传陪餐记录照片。</p>
                        <p>适用于：{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="qrcode-url no-print">
                        <small>{{ upload_url }}</small>
                    </div>
                    <div class="qrcode-actions no-print">
                        <a href="{{ upload_url }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt mr-1"></i> 访问链接
                        </a>
                        <button class="btn btn-success" onclick="copyToClipboard('{{ upload_url }}')">
                            <i class="fas fa-copy mr-1"></i> 复制链接
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评分照片二维码 -->
        <div class="col-lg-6">
            <div class="qrcode-card">
                <div class="card-header">
                    <i class="fas fa-star mr-2"></i> 评价陪餐记录照片
                </div>
                <div class="card-body">
                    <img src="data:image/png;base64,{{ rating_qrcode_base64 }}" alt="评价照片二维码" class="qrcode-image">
                    <div class="qrcode-description">
                        <p>扫描上方二维码，评价陪餐记录照片。</p>
                        <p>适用于：{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="qrcode-url no-print">
                        <small>{{ rating_url }}</small>
                    </div>
                    <div class="qrcode-actions no-print">
                        <a href="{{ rating_url }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt mr-1"></i> 访问链接
                        </a>
                        <button class="btn btn-success" onclick="copyToClipboard('{{ rating_url }}')">
                            <i class="fas fa-copy mr-1"></i> 复制链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row no-print">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">使用说明</h6>
                </div>
                <div class="card-body">
                    <ol>
                        <li>将二维码打印出来或保存到手机，方便员工扫描。</li>
                        <li>员工可以通过扫描左侧二维码上传陪餐记录照片。</li>
                        <li>管理员和其他用户可以通过扫描右侧二维码评价照片。</li>
                        <li>上传的照片将自动关联到{{ log.log_date.strftime('%Y-%m-%d') }}的陪餐记录中。</li>
                        <li>照片评分将用于食堂管理质量评估。</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function copyToClipboard(text) {
        // 创建临时输入框
        const input = document.createElement('input');
        input.setAttribute('value', text);
        document.body.appendChild(input);

        // 选择并复制
        input.select();
        document.execCommand('copy');

        // 移除临时输入框
        document.body.removeChild(input);

        // 显示提示
        alert('链接已复制到剪贴板');
    }
</script>
{% endblock %}
