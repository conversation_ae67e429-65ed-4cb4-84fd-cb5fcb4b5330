{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .table th {
        font-size: 14px;
    }
    .table td {
        font-size: 14px;
    }
    .btn-group-sm > .btn, .btn-sm {
        padding: .25rem .5rem;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'trainings') }}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-chalkboard-teacher mr-1"></i> 培训记录列表
            </h6>
            <div>
                <a href="{{ url_for('daily_management.print_trainings', log_id=log.id) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-print mr-1"></i> 打印
                </a>
                <a href="{{ url_for('daily_management.add_training', log_id=log.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus mr-1"></i> 添加培训记录
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if trainings %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>培训主题</th>
                            <th>培训讲师</th>
                            <th>培训时间</th>
                            <th>培训地点</th>
                            <th>参训人数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for training in trainings %}
                        <tr>
                            <td>{{ training.training_topic }}</td>
                            <td>{{ training.trainer }}</td>
                            <td>{{ training.training_time|format_datetime }}</td>
                            <td>{{ training.location or '-' }}</td>
                            <td>{{ training.attendees_count or 0 }}</td>
                            <td>
                                <a href="{{ url_for('daily_management.view_training', training_id=training.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('daily_management.edit_training', training_id=training.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-danger btn-sm" data-id="{{ training.id }}" onclick="deleteTraining(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">暂无培训记录</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条培训记录吗？此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="post">
                    {{ csrf_token() }}
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function deleteTraining(button) {
    const id = button.getAttribute('data-id');
    const form = document.getElementById('deleteForm');
    form.action = "{{ url_for('daily_management.delete_training', training_id=0) }}".replace('0', id);
    $('#deleteModal').modal('show');
}
</script>
{% endblock %}
