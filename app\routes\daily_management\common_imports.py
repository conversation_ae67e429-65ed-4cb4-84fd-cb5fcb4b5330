"""
通用导入模块，包含所有日常管理模块需要的导入
"""
from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify, send_file, make_response
from flask_wtf import FlaskForm
from flask_login import login_required, current_user
from app import db
from app.routes.daily_management import daily_management_bp
from app.models_daily_management import (
    DailyLog, InspectionRecord, DiningCompanion,
    CanteenTrainingRecord, SpecialEvent, Issue, Photo,
    InspectionTemplate
)
from app.models import AdministrativeArea, User
from datetime import datetime, date, timedelta
from sqlalchemy import text
import os
from werkzeug.utils import secure_filename
from PIL import Image
from app.utils.qrcode_helper import generate_companion_qrcode, generate_qrcode_base64
from app.utils.decorators import check_permission
from app.utils.pdf_generator import generate_daily_summary_pdf
from app.utils.email_sender import send_daily_summary_email

# 辅助函数
def handle_photo_upload(photo, reference_type):
    """处理照片上传
    
    参数:
        photo: 上传的照片文件
        reference_type: 引用类型，如 'inspection', 'companion', 'training', 'event', 'issue'
        
    返回:
        照片保存路径
    """
    try:
        if not photo:
            current_app.logger.error("没有接收到照片文件")
            return None
            
        if not photo.filename:
            current_app.logger.error("照片文件名为空")
            return None
            
        # 确保文件名安全
        filename = secure_filename(photo.filename)
        
        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if '.' not in filename or filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            current_app.logger.error(f"不支持的文件类型: {filename}")
            return None
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_filename = f"{reference_type}_{timestamp}_{filename}"
        
        # 确保目录存在
        upload_folder = os.path.join(current_app.static_folder, 'uploads', reference_type)
        os.makedirs(upload_folder, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_folder, unique_filename)
        photo.save(file_path)
        
        # 检查文件是否成功保存
        if not os.path.exists(file_path):
            current_app.logger.error(f"文件保存失败: {file_path}")
            return None
            
        # 返回相对路径
        return f"/static/uploads/{reference_type}/{unique_filename}"
        
    except Exception as e:
        current_app.logger.error(f"处理照片上传时出错: {str(e)}")
        return None
