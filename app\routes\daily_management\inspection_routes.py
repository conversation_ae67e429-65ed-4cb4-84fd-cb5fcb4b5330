"""
检查记录相关功能模块
"""
from .common_imports import *
from datetime import datetime
from flask_wtf import FlaskForm
from werkzeug.utils import secure_filename
import os

# 检查模板管理
@daily_management_bp.route('/inspection-templates')
@login_required
def inspection_templates():
    """检查模板管理页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法访问检查模板管理', 'danger')
        return redirect(url_for('main.index'))

    # 获取URL参数
    log_id = request.args.get('log_id', type=int)
    inspection_type = request.args.get('inspection_type')

    return render_template('daily_management/inspection_templates.html',
                          title=f'{user_area.name} - 检查模板管理',
                          school=user_area,
                          log_id=log_id,
                          inspection_type=inspection_type)

@daily_management_bp.route('/simplified-inspection/<int:log_id>', methods=['GET'])
@login_required
def simplified_inspection(log_id):
    """检查记录简化视图

    这是检查记录的主要显示视图，其他视图都已重定向到此视图。

    参数:
        log_id: 日志ID

    返回:
        渲染后的检查记录页面
    """
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的检查记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取前一天和后一天的日志
    # 确保日期格式正确，避免DATETIME2精度问题
    if isinstance(log.log_date, str):
        if ' ' in log.log_date:
            # 包含时间部分
            log_date = datetime.strptime(log.log_date.split('.')[0], '%Y-%m-%d %H:%M:%S').date()
        else:
            # 只有日期部分
            log_date = datetime.strptime(log.log_date, '%Y-%m-%d').date()
    elif isinstance(log.log_date, datetime):
        log_date = log.log_date.date()
    else:
        log_date = log.log_date

    prev_log = DailyLog.query.filter(
        DailyLog.log_date < log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.desc()).first()

    next_log = DailyLog.query.filter(
        DailyLog.log_date > log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.asc()).first()

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='morning'
    ).order_by(InspectionRecord.inspection_time).all()

    noon_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='noon'
    ).order_by(InspectionRecord.inspection_time).all()

    evening_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='evening'
    ).order_by(InspectionRecord.inspection_time).all()

    # 获取照片列表（按检查类型分组）
    morning_photos = []
    noon_photos = []
    evening_photos = []

    try:
        # 获取早晨检查照片
        for inspection in morning_inspections:
            sql = text("""
            SELECT id, file_path, rating, upload_time
            FROM photos
            WHERE reference_type = 'inspection' AND reference_id = :reference_id
            ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {'reference_id': inspection.id})

            for row in result:
                morning_photos.append({
                    'id': row[0],
                    'file_path': row[1],
                    'rating': row[2],
                    'upload_time': row[3]
                })

        # 获取中午检查照片
        for inspection in noon_inspections:
            sql = text("""
            SELECT id, file_path, rating, upload_time
            FROM photos
            WHERE reference_type = 'inspection' AND reference_id = :reference_id
            ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {'reference_id': inspection.id})

            for row in result:
                noon_photos.append({
                    'id': row[0],
                    'file_path': row[1],
                    'rating': row[2],
                    'upload_time': row[3]
                })

        # 获取晚上检查照片
        for inspection in evening_inspections:
            sql = text("""
            SELECT id, file_path, rating, upload_time
            FROM photos
            WHERE reference_type = 'inspection' AND reference_id = :reference_id
            ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {'reference_id': inspection.id})

            for row in result:
                evening_photos.append({
                    'id': row[0],
                    'file_path': row[1],
                    'rating': row[2],
                    'upload_time': row[3]
                })

    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    return render_template('daily_management/simplified_inspection.html',
                          title='检查记录',
                          log=log,
                          school=user_area,  # 添加学校信息
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          morning_photos=morning_photos,  # 添加照片列表
                          noon_photos=noon_photos,
                          evening_photos=evening_photos,
                          form=form,  # 添加表单对象
                          today=today,
                          timedelta=timedelta)

# 通过日期访问检查记录
@daily_management_bp.route('/inspections/date/<date_str>', methods=['GET'])
@login_required
def inspections_by_date(date_str):
    """通过日期访问检查记录

    参数:
        date_str: 日期字符串，格式为 YYYY-MM-DD

    返回:
        重定向到对应日期的检查记录页面
    """
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date).first()

        # 获取视图类型参数
        view_type = request.args.get('view', 'card')

        # 如果找到日志，重定向到检查记录页面
        if log:
            if view_type == 'table':
                return redirect(url_for('daily_management.inspections_table', log_id=log.id))
            elif view_type == 'card_layout':
                return redirect(url_for('daily_management.inspections_card_layout', log_id=log.id))
            elif view_type == 'simple_table':
                return redirect(url_for('daily_management.inspections_simple_table', log_id=log.id))
            elif view_type == 'category_cards':
                return redirect(url_for('daily_management.inspections_category_cards', log_id=log.id))
            elif view_type == 'simplified':
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))
            else:
                # 默认使用简化检查记录页面
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

        # 如果没有找到日志，创建一个新的日志
        sql = text("""
        INSERT INTO daily_logs
        (log_date, area_id, created_by)
        OUTPUT inserted.id
        VALUES
        (CONVERT(DATETIME2(1), :log_date, 120), :area_id, :created_by)
        """)

        # 准备参数
        params = {
            'log_date': log_date.strftime('%Y-%m-%d'),
            'area_id': current_user.area_id,
            'created_by': current_user.id
        }

        # 执行 SQL
        result = db.session.execute(sql, params)
        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的检查记录页面
        if view_type == 'table':
            return redirect(url_for('daily_management.inspections_table', log_id=log_id))
        elif view_type == 'card_layout':
            return redirect(url_for('daily_management.inspections_card_layout', log_id=log_id))
        elif view_type == 'simple_table':
            return redirect(url_for('daily_management.inspections_simple_table', log_id=log_id))
        elif view_type == 'category_cards':
            return redirect(url_for('daily_management.inspections_category_cards', log_id=log_id))
        elif view_type == 'simplified':
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
        else:
            # 默认使用简化检查记录页面
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
    except Exception as e:
        flash(f'访问检查记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 添加检查记录
@daily_management_bp.route('/api/inspections/add', methods=['POST'])
@login_required
def add_inspection():
    """添加检查记录API"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的检查记录'}), 403

        # 创建检查记录
        inspection = InspectionRecord(
            daily_log_id=log_id,
            inspection_type=data.get('inspection_type'),
            category=data.get('category'),
            item_name=data.get('item_name'),
            status=data.get('status'),
            description=data.get('description'),
            inspector=data.get('inspector') or current_user.real_name or current_user.username,
            inspection_time=datetime.now().replace(microsecond=0),
            created_by=current_user.id
        )

        db.session.add(inspection)
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'inspection')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_name=photo_file.filename,
                    file_path=file_path,
                    reference_type='inspection',
                    reference_id=inspection.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录添加成功',
            'inspection': {
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加检查记录失败: {str(e)}'}), 500

# 更新检查记录
@daily_management_bp.route('/api/inspections/update/<int:inspection_id>', methods=['POST'])
@login_required
def update_inspection(inspection_id):
    """更新检查记录API"""
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取日志
        log = DailyLog.query.get_or_404(inspection.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该检查记录'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 更新检查记录
        inspection.inspection_type = data.get('inspection_type', inspection.inspection_type)
        inspection.category = data.get('category', inspection.category)
        inspection.item_name = data.get('item_name', inspection.item_name)
        inspection.status = data.get('status', inspection.status)
        inspection.description = data.get('description', inspection.description)
        inspection.inspector = data.get('inspector', inspection.inspector)
        inspection.updated_at = datetime.now().replace(microsecond=0)

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'inspection')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_name=photo_file.filename,
                    file_path=file_path,
                    reference_type='inspection',
                    reference_id=inspection.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录更新成功',
            'inspection': {
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新检查记录失败: {str(e)}'}), 500

# 删除检查记录
@daily_management_bp.route('/api/inspections/delete/<int:inspection_id>', methods=['POST'])
@login_required
def delete_inspection(inspection_id):
    """删除检查记录API"""
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取日志
        log = DailyLog.query.get_or_404(inspection.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该检查记录'}), 403

        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 删除检查记录
        db.session.delete(inspection)
        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除检查记录失败: {str(e)}'}), 500

# 获取检查记录
@daily_management_bp.route('/api/inspections/<int:log_id>', methods=['GET'])
@login_required
def get_inspections(log_id):
    """获取检查记录API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的检查记录'}), 403

        # 获取检查类型参数
        inspection_type = request.args.get('type')

        # 查询检查记录
        query = InspectionRecord.query.filter_by(daily_log_id=log_id)

        # 如果指定了检查类型，则按类型筛选
        if inspection_type:
            query = query.filter_by(inspection_type=inspection_type)

        # 获取检查记录
        inspections = query.order_by(InspectionRecord.inspection_time).all()

        # 构建响应数据
        result = []
        for inspection in inspections:
            # 获取照片
            photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection.id).all()
            photo_list = []
            for photo in photos:
                photo_list.append({
                    'id': photo.id,
                    'file_path': photo.file_path,
                    'upload_time': photo.upload_time.strftime('%Y-%m-%d %H:%M:%S') if photo.upload_time else None
                })

            # 添加检查记录
            result.append({
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S') if inspection.inspection_time else None,
                'photos': photo_list
            })

        # 返回成功响应
        return jsonify({
            'success': True,
            'inspections': result
        })

    except Exception as e:
        current_app.logger.error(f"获取检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取检查记录失败: {str(e)}'}), 500

@daily_management_bp.route('/add-inspection-photo/<int:log_id>/<string:type>', methods=['GET', 'POST'])
@login_required
def add_inspection_photo(log_id, type):
    """添加检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限添加该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    form = FlaskForm()

    if request.method == 'POST':
        try:
            photo_file = request.files.get('photo')
            rating = request.form.get('rating', type=int) or 3
            description = request.form.get('description', '')
            inspection_type = request.form.get('inspection_type') or type
            inspection_date = request.form.get('inspection_date')

            if photo_file and photo_file.filename:
                # 首先创建检查记录
                sql = text("""
                    INSERT INTO inspection_records
                    (daily_log_id, inspection_type, inspection_item, status, description, inspection_time)
                    OUTPUT inserted.id
                    VALUES
                    (:daily_log_id, :inspection_type, :inspection_item, :status, :description, :inspection_time)
                """)

                result = db.session.execute(sql, {
                    'daily_log_id': log_id,
                    'inspection_type': inspection_type,
                    'inspection_item': '照片记录',
                    'status': 'normal',
                    'description': description,
                    'inspection_time': datetime.now().replace(microsecond=0)
                })

                inspection_id = result.fetchone()[0]

                # 确保文件名安全
                filename = secure_filename(photo_file.filename)

                # 生成唯一文件名
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                unique_filename = f"{timestamp}_{filename}"

                # 确定保存路径
                upload_folder = os.path.join(
                    current_app.static_folder,
                    'uploads',
                    'daily_management',
                    'inspection'
                )

                # 确保目录存在
                os.makedirs(upload_folder, exist_ok=True)

                # 保存文件
                file_path = os.path.join(upload_folder, unique_filename)
                photo_file.save(file_path)

                # 相对路径（用于数据库存储）
                relative_path = f"/static/uploads/daily_management/inspection/{unique_filename}"

                # 使用原始 SQL 创建照片记录
                sql = text("""
                    INSERT INTO photos
                    (reference_id, reference_type, file_name, file_path, rating, description)
                    VALUES
                    (:reference_id, :reference_type, :file_name, :file_path, :rating, :description)
                """)

                db.session.execute(sql, {
                    'reference_id': inspection_id,  # 使用检查记录ID而不是日志ID
                    'reference_type': 'inspection',
                    'file_name': filename,
                    'file_path': relative_path,
                    'rating': rating,
                    'description': description
                })

                db.session.commit()

                flash('照片上传成功', 'success')
                return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
            else:
                flash('请选择要上传的照片', 'warning')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传照片失败: {str(e)}")
            import traceback
            current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
            flash(f'上传照片失败: {str(e)}', 'danger')

    return render_template('daily_management/add_inspection_photo.html',
                          title='添加检查照片',
                          log=log,
                          type=type,
                          form=form)

@daily_management_bp.route('/generate-photo-upload-qrcode/<int:log_id>', methods=['GET'])
@login_required
def generate_photo_upload_qrcode(log_id):
    """生成照片上传二维码"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限生成该日志的照片上传二维码', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 生成上传照片的URL
    upload_url = url_for('daily_management.public_upload_inspection_photo',
                        school_id=user_area.id,
                        log_id=log_id,
                        inspection_type='morning',
                        _external=True)

    # 生成二维码
    qrcode_base64 = generate_qrcode_base64(upload_url)

    return render_template('daily_management/photo_upload_qrcode.html',
                          title=f'{user_area.name} - 照片上传二维码',
                          log=log,
                          school=user_area,
                          qrcode_base64=qrcode_base64,
                          upload_url=upload_url)

@daily_management_bp.route('/rate-inspection-photos/<int:log_id>', methods=['GET'])
@login_required
def rate_inspection_photos(log_id):
    """评分检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限评分该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    return render_template('daily_management/rate_inspection_photos.html',
                          title=f'{user_area.name} - 评分检查照片',
                          log=log,
                          school=user_area)

# 查看检查照片详情
@daily_management_bp.route('/view-inspection-photo/<int:photo_id>', methods=['GET'])
@login_required
def view_inspection_photo(photo_id):
    """查看检查照片详情"""
    try:
        # 使用原始SQL查询获取照片信息
        sql = text("""
        SELECT p.id, p.file_path, p.rating, p.upload_time, p.description,
               p.reference_id, ir.daily_log_id, ir.inspection_type
        FROM photos p
        LEFT JOIN inspection_records ir ON p.reference_id = ir.id
        WHERE p.id = :photo_id AND p.reference_type = 'inspection'
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 获取日志信息
        log = DailyLog.query.get_or_404(photo_data.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            flash('您没有权限查看该照片', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 构建照片对象
        photo = {
            'id': photo_data.id,
            'file_path': photo_data.file_path,
            'rating': photo_data.rating,
            'upload_time': photo_data.upload_time,
            'description': photo_data.description,
            'reference_id': photo_data.reference_id,
            'inspection_type': photo_data.inspection_type
        }

        return render_template('daily_management/view_inspection_photo.html',
                              title='查看检查照片',
                              photo=photo,
                              log=log,
                              school=user_area)

    except Exception as e:
        current_app.logger.error(f"查看检查照片失败: {str(e)}")
        flash(f'查看检查照片失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))

# 编辑检查照片
@daily_management_bp.route('/edit-inspection-photo/<int:photo_id>', methods=['GET', 'POST'])
@login_required
def edit_inspection_photo(photo_id):
    """编辑检查照片"""
    try:
        # 使用原始SQL查询获取照片信息
        sql = text("""
        SELECT p.id, p.file_path, p.rating, p.upload_time, p.description,
               p.reference_id, ir.daily_log_id, ir.inspection_type
        FROM photos p
        LEFT JOIN inspection_records ir ON p.reference_id = ir.id
        WHERE p.id = :photo_id AND p.reference_type = 'inspection'
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 获取日志信息
        log = DailyLog.query.get_or_404(photo_data.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            flash('您没有权限编辑该照片', 'danger')
            return redirect(url_for('daily_management.logs'))

        form = FlaskForm()

        if request.method == 'POST':
            # 获取表单数据
            rating = request.form.get('rating', type=int) or 3
            description = request.form.get('description', '')

            # 更新照片信息
            update_sql = text("""
            UPDATE photos
            SET rating = :rating, description = :description
            WHERE id = :photo_id
            """)

            db.session.execute(update_sql, {
                'rating': rating,
                'description': description,
                'photo_id': photo_id
            })

            db.session.commit()
            flash('照片信息更新成功', 'success')
            return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

        # 构建照片对象
        photo = {
            'id': photo_data.id,
            'file_path': photo_data.file_path,
            'rating': photo_data.rating,
            'upload_time': photo_data.upload_time,
            'description': photo_data.description,
            'reference_id': photo_data.reference_id,
            'inspection_type': photo_data.inspection_type
        }

        return render_template('daily_management/edit_inspection_photo.html',
                              title='编辑检查照片',
                              photo=photo,
                              log=log,
                              school=user_area,
                              form=form)

    except Exception as e:
        current_app.logger.error(f"编辑检查照片失败: {str(e)}")
        flash(f'编辑检查照片失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))

# 删除检查照片
@daily_management_bp.route('/delete-inspection-photo/<int:photo_id>', methods=['POST'])
@login_required
def delete_inspection_photo(photo_id):
    """删除检查照片"""
    try:
        # 使用原始SQL查询获取照片信息
        sql = text("""
        SELECT p.id, p.file_path, p.reference_id, ir.daily_log_id
        FROM photos p
        LEFT JOIN inspection_records ir ON p.reference_id = ir.id
        WHERE p.id = :photo_id AND p.reference_type = 'inspection'
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 获取日志信息
        log = DailyLog.query.get_or_404(photo_data.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            flash('您没有权限删除该照片', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 删除文件
        file_path = os.path.join(current_app.static_folder, photo_data.file_path.lstrip('/static/'))
        if os.path.exists(file_path):
            os.remove(file_path)

        # 删除数据库记录
        delete_sql = text("""
        DELETE FROM photos
        WHERE id = :photo_id
        """)

        db.session.execute(delete_sql, {'photo_id': photo_id})
        db.session.commit()

        flash('照片删除成功', 'success')
        return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除检查照片失败: {str(e)}")
        flash(f'删除检查照片失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))

@daily_management_bp.route('/print-inspection-photos/<int:log_id>', methods=['GET'])
@login_required
def print_inspection_photos(log_id):
    """打印检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限打印该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    return render_template('daily_management/print_inspection_photos.html',
                          title=f'{user_area.name} - 打印检查照片',
                          log=log,
                          school=user_area)
