"""
公共访问功能模块
"""
from .common_imports import *

# 公共陪餐记录表单
@daily_management_bp.route('/public/companion/<int:log_id>', methods=['GET', 'POST'])
def public_companion_form(log_id):
    """公共陪餐记录表单，无需登录即可访问"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(log.area_id)

    if request.method == 'POST':
        try:
            # 获取表单数据
            companion_name = request.form.get('companion_name')
            position = request.form.get('position')
            dining_type = request.form.get('dining_type')
            feedback = request.form.get('feedback')
            rating = request.form.get('rating', type=int) or 5

            # 验证必填字段
            if not companion_name or not position or not dining_type:
                flash('请填写必填字段', 'danger')
                return render_template('daily_management/public/companion_form.html',
                                      title=f'{school.name} - 陪餐记录',
                                      log=log,
                                      school=school)

            # 创建陪餐记录
            companion = DiningCompanion(
                daily_log_id=log_id,
                companion_name=companion_name,
                companion_role=position,  # 映射到正确字段
                meal_type=dining_type,    # 映射到正确字段
                dining_time=datetime.now().replace(microsecond=0),
                comments=feedback,        # 映射到正确字段
                taste_rating=rating       # 映射到正确字段
            )

            db.session.add(companion)
            db.session.commit()

            # 处理照片上传
            photo_file = request.files.get('photo')
            if photo_file and photo_file.filename:
                file_path = handle_photo_upload(photo_file, 'companion')

                if file_path:
                    # 创建照片记录
                    photo = Photo(
                        file_name=photo_file.filename,
                        file_path=file_path,
                        reference_type='companion',
                        reference_id=companion.id,
                        upload_time=datetime.now().replace(microsecond=0)
                    )

                    db.session.add(photo)
                    db.session.commit()

            # 显示成功页面
            return render_template('daily_management/public/companion_success.html',
                                  title=f'{school.name} - 陪餐记录提交成功',
                                  log=log,
                                  school=school,
                                  companion=companion)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加陪餐记录失败: {str(e)}")
            flash(f'提交失败: {str(e)}', 'danger')

    # GET 请求，显示表单
    return render_template('daily_management/public/companion_form.html',
                          title=f'{school.name} - 陪餐记录',
                          log=log,
                          school=school)

# 公共检查记录二维码
@daily_management_bp.route('/inspection/qrcode/<int:log_id>')
@login_required
def inspection_qrcode(log_id):
    """生成检查记录二维码页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限生成该日志的检查记录二维码', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 生成二维码URL
    qrcode_url = url_for('daily_management.public_inspection_form', log_id=log_id, _external=True)

    # 生成二维码图片
    qrcode_base64 = generate_qrcode_base64(qrcode_url)

    return render_template('daily_management/inspection_qrcode.html',
                          title=f'{user_area.name} - 检查记录二维码',
                          log=log,
                          qrcode_base64=qrcode_base64,
                          qrcode_url=qrcode_url,
                          school=user_area)

# 公共检查记录表单
@daily_management_bp.route('/public/inspection/<int:log_id>', methods=['GET', 'POST'])
def public_inspection_form(log_id):
    """公共检查记录表单，无需登录即可访问"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(log.area_id)

    if request.method == 'POST':
        try:
            # 获取表单数据
            inspection_type = request.form.get('inspection_type')
            category = request.form.get('category')
            item_name = request.form.get('item_name')
            status = request.form.get('status')
            description = request.form.get('description')
            inspector = request.form.get('inspector')

            # 验证必填字段
            if not inspection_type or not category or not item_name or not status or not inspector:
                flash('请填写必填字段', 'danger')
                return render_template('daily_management/public/inspection_form.html',
                                      title=f'{school.name} - 检查记录',
                                      log=log,
                                      school=school)

            # 创建检查记录
            inspection = InspectionRecord(
                daily_log_id=log_id,
                inspection_type=inspection_type,
                inspection_item=item_name,  # 映射到正确字段
                status=status,
                description=description,
                inspection_time=datetime.now().replace(microsecond=0)
            )

            db.session.add(inspection)
            db.session.commit()

            # 处理照片上传
            photo_file = request.files.get('photo')
            if photo_file and photo_file.filename:
                file_path = handle_photo_upload(photo_file, 'inspection')

                if file_path:
                    # 创建照片记录
                    photo = Photo(
                        file_name=photo_file.filename,
                        file_path=file_path,
                        reference_type='inspection',
                        reference_id=inspection.id,
                        upload_time=datetime.now().replace(microsecond=0)
                    )

                    db.session.add(photo)
                    db.session.commit()

            # 显示成功页面
            return render_template('daily_management/public/inspection_success.html',
                                  title=f'{school.name} - 检查记录提交成功',
                                  log=log,
                                  school=school,
                                  inspection=inspection)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加检查记录失败: {str(e)}")
            flash(f'提交失败: {str(e)}', 'danger')

    # GET 请求，显示表单
    return render_template('daily_management/public/inspection_form.html',
                          title=f'{school.name} - 检查记录',
                          log=log,
                          school=school)
