"""
培训记录相关功能模块
"""
from .common_imports import *

# 培训记录页面
@daily_management_bp.route('/trainings/<int:log_id>', methods=['GET'])
@login_required
def trainings(log_id):
    """培训记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(
        DailyLog.log_date < log.log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.desc()).first()

    next_log = DailyLog.query.filter(
        DailyLog.log_date > log.log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.asc()).first()

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).order_by(CanteenTrainingRecord.training_time).all()

    # 获取每个培训记录的照片
    try:
        for training in trainings:
            # 使用原始SQL查询获取照片
            sql = text("""
            SELECT id, file_path, rating, upload_time
            FROM photos
            WHERE reference_type = 'training' AND reference_id = :reference_id
            ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {'reference_id': training.id})

            photos = []
            for row in result:
                photos.append({
                    'id': row[0],
                    'file_path': row[1],
                    'rating': row[2],
                    'upload_time': row[3]
                })

            training.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    return render_template('daily_management/trainings.html',
                          title=f'{user_area.name} - 培训记录',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          trainings=trainings,
                          today=today,
                          school=user_area)

# 通过日期访问培训记录
@daily_management_bp.route('/trainings/date/<date_str>', methods=['GET'])
@login_required
def trainings_by_date(date_str):
    """通过日期访问培训记录"""
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法查看培训记录', 'danger')
            return redirect(url_for('daily_management.index'))

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date, area_id=user_area.id).first()

        # 如果找到日志，重定向到培训记录页面
        if log:
            return redirect(url_for('daily_management.trainings', log_id=log.id))

        # 如果没有找到日志，创建一个新的日志
        log = DailyLog(
            log_date=log_date,
            area_id=user_area.id,
            manager=current_user.real_name or current_user.username,
            created_by=current_user.id
        )

        db.session.add(log)
        db.session.commit()

        # 重定向到新创建的日志的培训记录页面
        return redirect(url_for('daily_management.trainings', log_id=log.id))
    except Exception as e:
        flash(f'访问培训记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 添加培训记录API
@daily_management_bp.route('/api/trainings/add', methods=['POST'])
@login_required
def add_training_api():
    """添加培训记录API"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的培训记录'}), 403

        # 创建培训记录
        training = CanteenTrainingRecord(
            daily_log_id=log_id,
            training_topic=data.get('training_topic'),
            trainer=data.get('trainer') or current_user.real_name or current_user.username,
            training_time=datetime.now().replace(microsecond=0),
            attendees=data.get('attendees'),
            content=data.get('content'),
            created_by=current_user.id
        )

        db.session.add(training)
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'training')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_path=file_path,
                    reference_type='training',
                    reference_id=training.id,
                    upload_time=datetime.now().replace(microsecond=0),
                    uploaded_by=current_user.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '培训记录添加成功',
            'training': {
                'id': training.id,
                'training_topic': training.training_topic,
                'trainer': training.trainer,
                'training_time': training.training_time.strftime('%Y-%m-%d %H:%M:%S'),
                'attendees': training.attendees,
                'content': training.content
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加培训记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加培训记录失败: {str(e)}'}), 500

# 更新培训记录API
@daily_management_bp.route('/api/trainings/update/<int:training_id>', methods=['POST'])
@login_required
def update_training(training_id):
    """更新培训记录API"""
    try:
        # 获取培训记录
        training = CanteenTrainingRecord.query.get_or_404(training_id)

        # 获取日志
        log = DailyLog.query.get_or_404(training.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该培训记录'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 更新培训记录
        training.training_topic = data.get('training_topic', training.training_topic)
        training.trainer = data.get('trainer', training.trainer)
        training.attendees = data.get('attendees', training.attendees)
        training.content = data.get('content', training.content)
        training.updated_at = datetime.now().replace(microsecond=0)

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'training')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_path=file_path,
                    reference_type='training',
                    reference_id=training.id,
                    upload_time=datetime.now().replace(microsecond=0),
                    uploaded_by=current_user.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '培训记录更新成功',
            'training': {
                'id': training.id,
                'training_topic': training.training_topic,
                'trainer': training.trainer,
                'training_time': training.training_time.strftime('%Y-%m-%d %H:%M:%S'),
                'attendees': training.attendees,
                'content': training.content
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新培训记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新培训记录失败: {str(e)}'}), 500

# 删除培训记录API
@daily_management_bp.route('/api/trainings/delete/<int:training_id>', methods=['POST'])
@login_required
def delete_training(training_id):
    """删除培训记录API"""
    try:
        # 获取培训记录
        training = CanteenTrainingRecord.query.get_or_404(training_id)

        # 获取日志
        log = DailyLog.query.get_or_404(training.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该培训记录'}), 403

        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='training', reference_id=training_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 删除培训记录
        db.session.delete(training)
        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '培训记录删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除培训记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除培训记录失败: {str(e)}'}), 500

# 获取培训记录API
@daily_management_bp.route('/api/trainings/<int:log_id>', methods=['GET'])
@login_required
def get_trainings(log_id):
    """获取培训记录API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的培训记录'}), 403

        # 获取培训记录
        trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).order_by(CanteenTrainingRecord.training_time).all()

        # 构建响应数据
        result = []
        for training in trainings:
            # 获取照片
            photos = Photo.query.filter_by(reference_type='training', reference_id=training.id).all()
            photo_list = []
            for photo in photos:
                photo_list.append({
                    'id': photo.id,
                    'file_path': photo.file_path,
                    'upload_time': photo.upload_time.strftime('%Y-%m-%d %H:%M:%S') if photo.upload_time else None
                })

            # 添加培训记录
            result.append({
                'id': training.id,
                'training_topic': training.training_topic,
                'trainer': training.trainer,
                'training_time': training.training_time.strftime('%Y-%m-%d %H:%M:%S') if training.training_time else None,
                'attendees': training.attendees,
                'content': training.content,
                'photos': photo_list
            })

        # 返回成功响应
        return jsonify({
            'success': True,
            'trainings': result
        })

    except Exception as e:
        current_app.logger.error(f"获取培训记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取培训记录失败: {str(e)}'}), 500

# 添加培训记录页面
@daily_management_bp.route('/trainings/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_training(log_id):
    """添加培训记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限添加该日志的培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            training_topic = request.form.get('training_topic')
            trainer = request.form.get('trainer') or current_user.real_name or current_user.username
            training_time = request.form.get('training_time')
            location = request.form.get('location')
            attendees = request.form.get('attendees')
            attendees_count = request.form.get('attendees_count', type=int) or 0
            content = request.form.get('content')

            # 验证必填字段
            if not training_topic:
                flash('请填写培训主题', 'danger')
                return render_template('daily_management/add_training.html',
                                      title=f'{user_area.name} - 添加培训记录',
                                      log=log,
                                      form=form,
                                      school=user_area)

            # 处理培训时间
            if training_time:
                try:
                    training_time = datetime.strptime(training_time, '%H:%M').time()
                    training_datetime = datetime.combine(log.log_date, training_time)
                except ValueError:
                    training_datetime = datetime.now().replace(microsecond=0)
            else:
                training_datetime = datetime.now().replace(microsecond=0)

            # 创建培训记录
            training = CanteenTrainingRecord(
                daily_log_id=log_id,
                training_topic=training_topic,
                trainer=trainer,
                training_time=training_datetime,
                location=location,
                attendees=attendees,
                attendees_count=attendees_count,
                content=content,
                created_by=current_user.id
            )

            db.session.add(training)
            db.session.commit()

            # 处理照片上传
            photo_files = request.files.getlist('photos')
            for photo_file in photo_files:
                if photo_file and photo_file.filename:
                    file_path = handle_photo_upload(photo_file, 'training')

                    if file_path:
                        # 创建照片记录
                        photo = Photo(
                            file_path=file_path,
                            reference_type='training',
                            reference_id=training.id,
                            upload_time=datetime.now().replace(microsecond=0),
                            uploaded_by=current_user.id
                        )

                        db.session.add(photo)

            db.session.commit()

            flash('培训记录添加成功', 'success')
            return redirect(url_for('daily_management.trainings', log_id=log_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加培训记录失败: {str(e)}")
            flash(f'添加培训记录失败: {str(e)}', 'danger')

    # GET 请求，显示表单
    return render_template('daily_management/add_training.html',
                          title=f'{user_area.name} - 添加培训记录',
                          log=log,
                          form=form,
                          school=user_area)

# 查看培训记录详情
@daily_management_bp.route('/trainings/view/<int:training_id>')
@login_required
def view_training(training_id):
    """查看培训记录详情"""
    # 获取培训记录
    training = CanteenTrainingRecord.query.get_or_404(training_id)

    # 获取日志
    log = DailyLog.query.get_or_404(training.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取照片
    photos = Photo.query.filter_by(reference_type='training', reference_id=training_id).all()

    return render_template('daily_management/view_training.html',
                          title=f'{user_area.name} - 培训记录详情',
                          training=training,
                          log=log,
                          photos=photos,
                          school=user_area)

# 编辑培训记录
@daily_management_bp.route('/trainings/edit/<int:training_id>', methods=['GET', 'POST'])
@login_required
def edit_training(training_id):
    """编辑培训记录"""
    # 获取培训记录
    training = CanteenTrainingRecord.query.get_or_404(training_id)

    # 获取日志
    log = DailyLog.query.get_or_404(training.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限编辑该培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            training.training_topic = request.form.get('training_topic', training.training_topic)
            training.trainer = request.form.get('trainer', training.trainer)

            # 处理培训时间
            training_time = request.form.get('training_time')
            if training_time:
                try:
                    training_time = datetime.strptime(training_time, '%H:%M').time()
                    training.training_time = datetime.combine(log.log_date, training_time)
                except ValueError:
                    pass  # 保持原有时间

            training.location = request.form.get('location', training.location)
            training.attendees = request.form.get('attendees', training.attendees)
            training.attendees_count = request.form.get('attendees_count', type=int) or training.attendees_count
            training.content = request.form.get('content', training.content)
            training.updated_at = datetime.now().replace(microsecond=0)

            db.session.commit()

            # 处理照片上传
            photo_files = request.files.getlist('photos')
            for photo_file in photo_files:
                if photo_file and photo_file.filename:
                    file_path = handle_photo_upload(photo_file, 'training')

                    if file_path:
                        # 创建照片记录
                        photo = Photo(
                            file_path=file_path,
                            reference_type='training',
                            reference_id=training.id,
                            upload_time=datetime.now().replace(microsecond=0),
                            uploaded_by=current_user.id
                        )

                        db.session.add(photo)

            db.session.commit()

            flash('培训记录更新成功', 'success')
            return redirect(url_for('daily_management.view_training', training_id=training_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新培训记录失败: {str(e)}")
            flash(f'更新培训记录失败: {str(e)}', 'danger')

    # GET 请求，显示编辑表单
    return render_template('daily_management/edit_training.html',
                          title=f'{user_area.name} - 编辑培训记录',
                          training=training,
                          log=log,
                          form=form,
                          school=user_area)

# 删除培训记录
@daily_management_bp.route('/trainings/delete/<int:training_id>', methods=['POST'])
@login_required
def delete_training(training_id):
    """删除培训记录"""
    # 获取培训记录
    training = CanteenTrainingRecord.query.get_or_404(training_id)

    # 获取日志
    log = DailyLog.query.get_or_404(training.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限删除该培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    try:
        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='training', reference_id=training_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 删除培训记录
        db.session.delete(training)
        db.session.commit()

        flash('培训记录删除成功', 'success')
        return redirect(url_for('daily_management.trainings', log_id=log.id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除培训记录失败: {str(e)}")
        flash(f'删除培训记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.trainings', log_id=log.id))