"""
陪餐记录相关功能模块
"""
from .common_imports import *

# 陪餐记录页面
@daily_management_bp.route('/companions/<int:log_id>', methods=['GET'])
@login_required
def companions(log_id):
    """陪餐记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取前一天和后一天的日志
    # 确保日期格式正确，避免DATETIME2精度问题
    if isinstance(log.log_date, str):
        if ' ' in log.log_date:
            # 包含时间部分
            log_date = datetime.strptime(log.log_date.split('.')[0], '%Y-%m-%d %H:%M:%S').date()
        else:
            # 只有日期部分
            log_date = datetime.strptime(log.log_date, '%Y-%m-%d').date()
    elif isinstance(log.log_date, datetime):
        log_date = log.log_date.date()
    else:
        log_date = log.log_date

    prev_log = DailyLog.query.filter(
        DailyLog.log_date < log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.desc()).first()

    next_log = DailyLog.query.filter(
        DailyLog.log_date > log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.asc()).first()

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log_id).order_by(DiningCompanion.dining_time).all()

    # 获取每个陪餐记录的照片
    try:
        for companion in companions:
            # 使用原始SQL查询获取照片
            sql = text("""
            SELECT id, file_path, rating, upload_time
            FROM photos
            WHERE reference_type = 'companion' AND reference_id = :reference_id
            ORDER BY upload_time DESC
            """)

            result = db.session.execute(sql, {'reference_id': companion.id})

            photos = []
            for row in result:
                photos.append({
                    'id': row[0],
                    'file_path': row[1],
                    'rating': row[2],
                    'upload_time': row[3]
                })

            companion.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    return render_template('daily_management/companions.html',
                          title=f'{user_area.name} - 陪餐记录',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          companions=companions,
                          today=today,
                          school=user_area)

# 通过日期访问陪餐记录
@daily_management_bp.route('/companions/date/<date_str>', methods=['GET'])
@login_required
def companions_by_date(date_str):
    """通过日期访问陪餐记录"""
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法查看陪餐记录', 'danger')
            return redirect(url_for('daily_management.index'))

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date, area_id=user_area.id).first()

        # 如果找到日志，重定向到陪餐记录页面
        if log:
            return redirect(url_for('daily_management.companions', log_id=log.id))

        # 如果没有找到日志，创建一个新的日志
        log = DailyLog(
            log_date=log_date,
            area_id=user_area.id,
            manager=current_user.real_name or current_user.username,
            created_by=current_user.id
        )

        db.session.add(log)
        db.session.commit()

        # 重定向到新创建的日志的陪餐记录页面
        return redirect(url_for('daily_management.companions', log_id=log.id))
    except Exception as e:
        flash(f'访问陪餐记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 生成陪餐记录二维码
@daily_management_bp.route('/companions/qrcode/<int:log_id>')
@login_required
def generate_companion_qrcode_view(log_id):
    """生成陪餐记录二维码页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限生成该日志的陪餐记录二维码', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 生成二维码URL
    qrcode_url = url_for('daily_management.public_companion_form', log_id=log_id, _external=True)

    # 生成二维码图片
    qrcode_base64 = generate_qrcode_base64(qrcode_url)

    return render_template('daily_management/companion_qrcode.html',
                          title=f'{user_area.name} - 陪餐记录二维码',
                          log=log,
                          qrcode_base64=qrcode_base64,
                          qrcode_url=qrcode_url,
                          school=user_area)

# 添加陪餐记录页面
@daily_management_bp.route('/companions/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_companion(log_id):
    """添加陪餐记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限添加该日志的陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            companion_name = request.form.get('companion_name')
            companion_role = request.form.get('companion_role')
            meal_type = request.form.get('meal_type')
            dining_time = request.form.get('dining_time')
            taste_rating = request.form.get('taste_rating', type=int) or 5
            hygiene_rating = request.form.get('hygiene_rating', type=int) or 5
            service_rating = request.form.get('service_rating', type=int) or 5
            comments = request.form.get('comments')
            suggestions = request.form.get('suggestions')

            # 验证必填字段
            if not companion_name or not companion_role or not meal_type:
                flash('请填写必填字段', 'danger')
                return render_template('daily_management/add_companion.html',
                                      title=f'{user_area.name} - 添加陪餐记录',
                                      log=log,
                                      form=form,
                                      school=user_area)

            # 处理用餐时间
            if dining_time:
                try:
                    dining_time = datetime.strptime(dining_time, '%H:%M').time()
                    # 确保 log.log_date 是 date 对象
                    if isinstance(log.log_date, str):
                        log_date = datetime.strptime(log.log_date, '%Y-%m-%d').date()
                    elif isinstance(log.log_date, datetime):
                        log_date = log.log_date.date()
                    else:
                        log_date = log.log_date
                    dining_datetime = datetime.combine(log_date, dining_time)
                except ValueError:
                    dining_datetime = datetime.now().replace(microsecond=0)
            else:
                dining_datetime = datetime.now().replace(microsecond=0)

            # 使用原始SQL创建陪餐记录，避免DATETIME2精度问题
            from sqlalchemy import text

            sql = text("""
                INSERT INTO dining_companions
                (daily_log_id, companion_name, companion_role, meal_type, dining_time,
                 taste_rating, hygiene_rating, service_rating, comments, suggestions)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :companion_name, :companion_role, :meal_type, :dining_time,
                 :taste_rating, :hygiene_rating, :service_rating, :comments, :suggestions)
            """)

            result = db.session.execute(sql, {
                'daily_log_id': log_id,
                'companion_name': companion_name,
                'companion_role': companion_role,
                'meal_type': meal_type,
                'dining_time': dining_datetime,
                'taste_rating': taste_rating,
                'hygiene_rating': hygiene_rating,
                'service_rating': service_rating,
                'comments': comments,
                'suggestions': suggestions
            })

            companion_id = result.fetchone()[0]
            db.session.commit()

            # 处理照片上传
            photo_files = request.files.getlist('photos')
            for photo_file in photo_files:
                if photo_file and photo_file.filename:
                    file_path = handle_photo_upload(photo_file, 'companion')

                    if file_path:
                        # 使用原始SQL创建照片记录
                        photo_sql = text("""
                            INSERT INTO photos
                            (reference_id, reference_type, file_name, file_path)
                            VALUES
                            (:reference_id, :reference_type, :file_name, :file_path)
                        """)

                        db.session.execute(photo_sql, {
                            'reference_id': companion_id,
                            'reference_type': 'companion',
                            'file_name': photo_file.filename,
                            'file_path': file_path
                        })

            db.session.commit()

            flash('陪餐记录添加成功', 'success')
            return redirect(url_for('daily_management.companions', log_id=log_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加陪餐记录失败: {str(e)}")
            flash(f'添加陪餐记录失败: {str(e)}', 'danger')

    # GET 请求，显示表单
    return render_template('daily_management/add_companion.html',
                          title=f'{user_area.name} - 添加陪餐记录',
                          log=log,
                          form=form,
                          school=user_area)

# 添加陪餐记录API
@daily_management_bp.route('/api/companions/add', methods=['POST'])
@login_required
def add_companion_api():
    """添加陪餐记录API"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的陪餐记录'}), 403

        # 使用原始SQL创建陪餐记录，避免DATETIME2精度问题
        from sqlalchemy import text

        sql = text("""
            INSERT INTO dining_companions
            (daily_log_id, companion_name, companion_role, meal_type, dining_time,
             comments, taste_rating)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :companion_name, :companion_role, :meal_type, :dining_time,
             :comments, :taste_rating)
        """)

        result = db.session.execute(sql, {
            'daily_log_id': log_id,
            'companion_name': data.get('companion_name'),
            'companion_role': data.get('position', '教师'),
            'meal_type': data.get('dining_type', 'lunch'),
            'dining_time': datetime.now().replace(microsecond=0),
            'comments': data.get('feedback'),
            'taste_rating': data.get('rating', type=int) or 5
        })

        companion_id = result.fetchone()[0]
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'companion')

            if file_path:
                # 使用原始SQL创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (reference_id, reference_type, file_name, file_path)
                    VALUES
                    (:reference_id, :reference_type, :file_name, :file_path)
                """)

                db.session.execute(photo_sql, {
                    'reference_id': companion_id,
                    'reference_type': 'companion',
                    'file_name': photo_file.filename,
                    'file_path': file_path
                })

                db.session.commit()

        # 获取创建的记录用于返回
        companion = DiningCompanion.query.get(companion_id)

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '陪餐记录添加成功',
            'companion': {
                'id': companion.id,
                'companion_name': companion.companion_name,
                'companion_role': companion.companion_role,
                'meal_type': companion.meal_type,
                'dining_time': companion.dining_time.strftime('%Y-%m-%d %H:%M:%S'),
                'comments': companion.comments,
                'taste_rating': companion.taste_rating
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加陪餐记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加陪餐记录失败: {str(e)}'}), 500

# 更新陪餐记录API
@daily_management_bp.route('/api/companions/update/<int:companion_id>', methods=['POST'])
@login_required
def update_companion(companion_id):
    """更新陪餐记录API"""
    try:
        # 获取陪餐记录
        companion = DiningCompanion.query.get_or_404(companion_id)

        # 获取日志
        log = DailyLog.query.get_or_404(companion.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该陪餐记录'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 使用原始SQL更新陪餐记录，避免DATETIME2精度问题
        from sqlalchemy import text

        update_sql = text("""
            UPDATE dining_companions
            SET companion_name = :companion_name,
                companion_role = :companion_role,
                meal_type = :meal_type,
                comments = :comments,
                taste_rating = :taste_rating
            WHERE id = :companion_id
        """)

        db.session.execute(update_sql, {
            'companion_name': data.get('companion_name', companion.companion_name),
            'companion_role': data.get('position', companion.companion_role),
            'meal_type': data.get('dining_type', companion.meal_type),
            'comments': data.get('feedback', companion.comments),
            'taste_rating': data.get('rating', type=int) or companion.taste_rating,
            'companion_id': companion_id
        })

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'companion')

            if file_path:
                # 使用原始SQL创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (reference_id, reference_type, file_name, file_path)
                    VALUES
                    (:reference_id, :reference_type, :file_name, :file_path)
                """)

                db.session.execute(photo_sql, {
                    'reference_id': companion_id,
                    'reference_type': 'companion',
                    'file_name': photo_file.filename,
                    'file_path': file_path
                })

                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '陪餐记录更新成功',
            'companion': {
                'id': companion.id,
                'companion_name': companion.companion_name,
                'companion_role': companion.companion_role,
                'meal_type': companion.meal_type,
                'dining_time': companion.dining_time.strftime('%Y-%m-%d %H:%M:%S'),
                'comments': companion.comments,
                'taste_rating': companion.taste_rating
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新陪餐记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新陪餐记录失败: {str(e)}'}), 500

# 删除陪餐记录API
@daily_management_bp.route('/api/companions/delete/<int:companion_id>', methods=['POST'])
@login_required
def delete_companion(companion_id):
    """删除陪餐记录API"""
    try:
        # 获取陪餐记录
        companion = DiningCompanion.query.get_or_404(companion_id)

        # 获取日志
        log = DailyLog.query.get_or_404(companion.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该陪餐记录'}), 403

        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='companion', reference_id=companion_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 删除陪餐记录
        db.session.delete(companion)
        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '陪餐记录删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除陪餐记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除陪餐记录失败: {str(e)}'}), 500

# 获取陪餐记录API
@daily_management_bp.route('/api/companions/<int:log_id>', methods=['GET'])
@login_required
def get_companions(log_id):
    """获取陪餐记录API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的陪餐记录'}), 403

        # 获取陪餐记录
        companions = DiningCompanion.query.filter_by(daily_log_id=log_id).order_by(DiningCompanion.dining_time).all()

        # 构建响应数据
        result = []
        for companion in companions:
            # 获取照片
            photos = Photo.query.filter_by(reference_type='companion', reference_id=companion.id).all()
            photo_list = []
            for photo in photos:
                photo_list.append({
                    'id': photo.id,
                    'file_path': photo.file_path,
                    'upload_time': photo.upload_time.strftime('%Y-%m-%d %H:%M:%S') if photo.upload_time else None
                })

            # 添加陪餐记录
            result.append({
                'id': companion.id,
                'companion_name': companion.companion_name,
                'companion_role': companion.companion_role,
                'meal_type': companion.meal_type,
                'dining_time': companion.dining_time.strftime('%Y-%m-%d %H:%M:%S') if companion.dining_time else None,
                'comments': companion.comments,
                'taste_rating': companion.taste_rating,
                'hygiene_rating': companion.hygiene_rating,
                'service_rating': companion.service_rating,
                'suggestions': companion.suggestions,
                'photos': photo_list
            })

        # 返回成功响应
        return jsonify({
            'success': True,
            'companions': result
        })

    except Exception as e:
        current_app.logger.error(f"获取陪餐记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取陪餐记录失败: {str(e)}'}), 500

# 查看陪餐记录详情
@daily_management_bp.route('/companions/view/<int:companion_id>')
@login_required
def view_companion(companion_id):
    """查看陪餐记录详情"""
    # 获取陪餐记录
    companion = DiningCompanion.query.get_or_404(companion_id)

    # 获取日志
    log = DailyLog.query.get_or_404(companion.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取照片
    photos = Photo.query.filter_by(reference_type='companion', reference_id=companion_id).all()

    return render_template('daily_management/view_companion.html',
                          title=f'{user_area.name} - 陪餐记录详情',
                          companion=companion,
                          log=log,
                          photos=photos,
                          school=user_area)

# 编辑陪餐记录
@daily_management_bp.route('/companions/edit/<int:companion_id>', methods=['GET', 'POST'])
@login_required
def edit_companion(companion_id):
    """编辑陪餐记录"""
    # 获取陪餐记录
    companion = DiningCompanion.query.get_or_404(companion_id)

    # 获取日志
    log = DailyLog.query.get_or_404(companion.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限编辑该陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            companion.companion_name = request.form.get('companion_name', companion.companion_name)
            companion.companion_role = request.form.get('companion_role', companion.companion_role)
            companion.meal_type = request.form.get('meal_type', companion.meal_type)

            # 处理用餐时间
            dining_time = request.form.get('dining_time')
            if dining_time:
                try:
                    dining_time = datetime.strptime(dining_time, '%H:%M').time()
                    # 确保 log.log_date 是 date 对象
                    if isinstance(log.log_date, str):
                        log_date = datetime.strptime(log.log_date, '%Y-%m-%d').date()
                    elif isinstance(log.log_date, datetime):
                        log_date = log.log_date.date()
                    else:
                        log_date = log.log_date
                    companion.dining_time = datetime.combine(log_date, dining_time)
                except ValueError:
                    pass  # 保持原有时间

            # 使用原始SQL更新陪餐记录，避免DATETIME2精度问题
            from sqlalchemy import text

            update_sql = text("""
                UPDATE dining_companions
                SET companion_name = :companion_name,
                    companion_role = :companion_role,
                    meal_type = :meal_type,
                    dining_time = :dining_time,
                    taste_rating = :taste_rating,
                    hygiene_rating = :hygiene_rating,
                    service_rating = :service_rating,
                    comments = :comments,
                    suggestions = :suggestions
                WHERE id = :companion_id
            """)

            db.session.execute(update_sql, {
                'companion_name': companion.companion_name,
                'companion_role': companion.companion_role,
                'meal_type': companion.meal_type,
                'dining_time': companion.dining_time,
                'taste_rating': request.form.get('taste_rating', type=int) or companion.taste_rating,
                'hygiene_rating': request.form.get('hygiene_rating', type=int) or companion.hygiene_rating,
                'service_rating': request.form.get('service_rating', type=int) or companion.service_rating,
                'comments': request.form.get('comments', companion.comments),
                'suggestions': request.form.get('suggestions', companion.suggestions),
                'companion_id': companion_id
            })

            db.session.commit()

            # 处理照片上传
            photo_files = request.files.getlist('photos')
            for photo_file in photo_files:
                if photo_file and photo_file.filename:
                    file_path = handle_photo_upload(photo_file, 'companion')

                    if file_path:
                        # 使用原始SQL创建照片记录
                        photo_sql = text("""
                            INSERT INTO photos
                            (reference_id, reference_type, file_name, file_path)
                            VALUES
                            (:reference_id, :reference_type, :file_name, :file_path)
                        """)

                        db.session.execute(photo_sql, {
                            'reference_id': companion_id,
                            'reference_type': 'companion',
                            'file_name': photo_file.filename,
                            'file_path': file_path
                        })

            db.session.commit()

            flash('陪餐记录更新成功', 'success')
            return redirect(url_for('daily_management.view_companion', companion_id=companion_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新陪餐记录失败: {str(e)}")
            flash(f'更新陪餐记录失败: {str(e)}', 'danger')

    # GET 请求，显示编辑表单
    return render_template('daily_management/edit_companion.html',
                          title=f'{user_area.name} - 编辑陪餐记录',
                          companion=companion,
                          log=log,
                          form=form,
                          school=user_area)
