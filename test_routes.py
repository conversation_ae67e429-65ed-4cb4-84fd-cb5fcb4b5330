#!/usr/bin/env python
"""
测试路由冲突修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    
    print("正在创建Flask应用...")
    app = create_app()
    
    print("✅ Flask应用创建成功！")
    print("✅ 路由冲突已修复！")
    
    # 显示一些关键路由
    print("\n📋 日常管理模块关键路由：")
    with app.app_context():
        for rule in app.url_map.iter_rules():
            if 'daily_management' in rule.endpoint:
                if 'add_training' in rule.endpoint or 'add_companion' in rule.endpoint:
                    print(f"  - {rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]")
    
    print("\n🎉 所有路由冲突已成功修复！")
    
except Exception as e:
    print(f"❌ 错误: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
