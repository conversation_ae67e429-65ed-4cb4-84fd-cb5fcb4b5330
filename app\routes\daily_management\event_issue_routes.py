"""
特殊事件和问题记录相关功能模块
"""
from .common_imports import *
from sqlalchemy import text
from sqlalchemy.dialects.mssql import DATETIME2

# 特殊事件页面
@daily_management_bp.route('/events/<int:log_id>', methods=['GET'])
@login_required
def events(log_id):
    """特殊事件页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的特殊事件', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取前一天和后一天的日志
    sql = text("""
        SELECT id, log_date, area_id, manager, created_at, updated_at
        FROM daily_logs
        WHERE area_id = :area_id
        AND log_date < :log_date
        ORDER BY log_date DESC
        OFFSET 0 ROWS
        FETCH NEXT 1 ROWS ONLY
    """)

    prev_log = db.session.execute(sql, {
        'area_id': user_area.id,
        'log_date': log.log_date if isinstance(log.log_date, str) else log.log_date.strftime('%Y-%m-%d')
    }).fetchone()

    sql = text("""
        SELECT id, log_date, area_id, manager, created_at, updated_at
        FROM daily_logs
        WHERE area_id = :area_id
        AND log_date > :log_date
        ORDER BY log_date ASC
        OFFSET 0 ROWS
        FETCH NEXT 1 ROWS ONLY
    """)

    next_log = db.session.execute(sql, {
        'area_id': user_area.id,
        'log_date': log.log_date if isinstance(log.log_date, str) else log.log_date.strftime('%Y-%m-%d')
    }).fetchone()

    # 获取特殊事件
    sql = text("""
        SELECT e.*, p.id as photo_id, p.file_path, p.rating, p.upload_time
        FROM special_events e
        LEFT JOIN photos p ON p.reference_type = 'event' AND p.reference_id = e.id
        WHERE e.daily_log_id = :log_id
        ORDER BY e.event_time
    """)

    result = db.session.execute(sql, {'log_id': log_id})

    # 处理查询结果
    events = []
    current_event = None

    for row in result:
        if not current_event or current_event['id'] != row.id:
            if current_event:
                events.append(current_event)

            current_event = {
                'id': row.id,
                'event_type': row.event_type,
                'event_name': row.event_name,
                'event_time': row.event_time.strftime('%Y-%m-%d %H:%M:%S') if row.event_time and not isinstance(row.event_time, str) else row.event_time,
                'description': row.description,
                'participants': row.participants,
                'created_at': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at and not isinstance(row.created_at, str) else row.created_at,
                'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M:%S') if row.updated_at and not isinstance(row.updated_at, str) else row.updated_at,
                'photos': []
            }

        if row.photo_id:
            current_event['photos'].append({
                'id': row.photo_id,
                'file_path': row.file_path,
                'rating': row.rating,
                'upload_time': row.upload_time.strftime('%Y-%m-%d %H:%M:%S') if row.upload_time and not isinstance(row.upload_time, str) else row.upload_time
            })

    if current_event:
        events.append(current_event)

    # 获取今天的日期，用于日期导航
    today = date.today()

    return render_template('daily_management/events.html',
                          title=f'{user_area.name} - 特殊事件',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          events=events,
                          today=today,
                          school=user_area)

# 问题记录页面
@daily_management_bp.route('/issues/<int:log_id>', methods=['GET'])
@login_required
def issues(log_id):
    """问题记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的问题记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取前一天和后一天的日志
    sql = text("""
        SELECT id, log_date, area_id, manager, created_at, updated_at
        FROM daily_logs
        WHERE area_id = :area_id
        AND log_date < :log_date
        ORDER BY log_date DESC
        OFFSET 0 ROWS
        FETCH NEXT 1 ROWS ONLY
    """)

    prev_log = db.session.execute(sql, {
        'area_id': user_area.id,
        'log_date': log.log_date if isinstance(log.log_date, str) else log.log_date.strftime('%Y-%m-%d')
    }).fetchone()

    sql = text("""
        SELECT id, log_date, area_id, manager, created_at, updated_at
        FROM daily_logs
        WHERE area_id = :area_id
        AND log_date > :log_date
        ORDER BY log_date ASC
        OFFSET 0 ROWS
        FETCH NEXT 1 ROWS ONLY
    """)

    next_log = db.session.execute(sql, {
        'area_id': user_area.id,
        'log_date': log.log_date if isinstance(log.log_date, str) else log.log_date.strftime('%Y-%m-%d')
    }).fetchone()

    # 获取问题记录
    sql = text("""
        SELECT i.*, p.id as photo_id, p.file_path, p.rating, p.upload_time
        FROM issues i
        LEFT JOIN photos p ON p.reference_type = 'issue' AND p.reference_id = i.id
        WHERE i.daily_log_id = :log_id
        ORDER BY i.found_time
    """)

    result = db.session.execute(sql, {'log_id': log_id})

    # 处理查询结果
    issues = []
    current_issue = None

    for row in result:
        if not current_issue or current_issue['id'] != row.id:
            if current_issue:
                issues.append(current_issue)

            current_issue = {
                'id': row.id,
                'issue_type': row.issue_type,
                'issue_name': row.issue_name,
                'found_time': row.found_time.strftime('%Y-%m-%d %H:%M:%S') if row.found_time and not isinstance(row.found_time, str) else row.found_time,
                'description': row.description,
                'status': row.status,
                'solution': row.solution,
                'resolved_time': row.resolved_time.strftime('%Y-%m-%d %H:%M:%S') if row.resolved_time and not isinstance(row.resolved_time, str) else row.resolved_time,
                'created_at': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at and not isinstance(row.created_at, str) else row.created_at,
                'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M:%S') if row.updated_at and not isinstance(row.updated_at, str) else row.updated_at,
                'photos': []
            }

        if row.photo_id:
            current_issue['photos'].append({
                'id': row.photo_id,
                'file_path': row.file_path,
                'rating': row.rating,
                'upload_time': row.upload_time.strftime('%Y-%m-%d %H:%M:%S') if row.upload_time and not isinstance(row.upload_time, str) else row.upload_time
            })

    if current_issue:
        issues.append(current_issue)

    # 获取今天的日期，用于日期导航
    today = date.today()

    return render_template('daily_management/issues.html',
                          title=f'{user_area.name} - 问题记录',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          issues=issues,
                          today=today,
                          school=user_area)

# 通过日期访问特殊事件
@daily_management_bp.route('/events/date/<date_str>', methods=['GET'])
@login_required
def events_by_date(date_str):
    """通过日期访问特殊事件"""
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法查看特殊事件', 'danger')
            return redirect(url_for('daily_management.index'))

        # 使用原始 SQL 查找对应日期的日志
        sql = text("""
            SELECT id, log_date, area_id, manager, created_at, updated_at
            FROM daily_logs
            WHERE log_date = :log_date AND area_id = :area_id
        """)

        result = db.session.execute(sql, {
            'log_date': log_date,
            'area_id': user_area.id
        }).fetchone()

        if result:
            # 如果找到日志，重定向到特殊事件页面
            return redirect(url_for('daily_management.events', log_id=result.id))

        # 如果没有找到日志，使用原始 SQL 创建新的日志
        insert_sql = text("""
            INSERT INTO daily_logs
            (log_date, area_id, manager, created_by)
            OUTPUT inserted.id
            VALUES
            (:log_date, :area_id, :manager, :created_by)
        """)

        result = db.session.execute(insert_sql, {
            'log_date': log_date,
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'created_by': current_user.id
        })

        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的特殊事件页面
        return redirect(url_for('daily_management.events', log_id=log_id))
    except Exception as e:
        flash(f'访问特殊事件失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 创建问题记录页面（自动创建日志）
@daily_management_bp.route('/issues/create', methods=['GET'])
@login_required
def create_issue():
    """创建问题记录页面（自动创建今日日志）"""
    try:
        # 获取今日日期
        today = datetime.now().date()

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法创建问题记录', 'danger')
            return redirect(url_for('daily_management.index'))

        # 使用原始 SQL 查找今日日志
        sql = text("""
            SELECT id, log_date, area_id, manager, created_at, updated_at
            FROM daily_logs
            WHERE log_date = :log_date AND area_id = :area_id
        """)

        result = db.session.execute(sql, {
            'log_date': today,
            'area_id': user_area.id
        }).fetchone()

        if result:
            # 如果找到日志，重定向到添加问题记录页面
            return redirect(url_for('daily_management.add_issue', log_id=result.id))

        # 如果没有找到日志，使用原始 SQL 创建新的日志
        insert_sql = text("""
            INSERT INTO daily_logs
            (log_date, area_id, manager, created_by)
            OUTPUT inserted.id
            VALUES
            (:log_date, :area_id, :manager, :created_by)
        """)

        result = db.session.execute(insert_sql, {
            'log_date': today,
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'created_by': current_user.id
        })

        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的添加问题记录页面
        return redirect(url_for('daily_management.add_issue', log_id=log_id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建问题记录失败: {str(e)}")
        flash(f'创建问题记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 通过日期访问问题记录
@daily_management_bp.route('/issues/date/<date_str>', methods=['GET'])
@login_required
def issues_by_date(date_str):
    """通过日期访问问题记录"""
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法查看问题记录', 'danger')
            return redirect(url_for('daily_management.index'))

        # 使用原始 SQL 查找对应日期的日志
        sql = text("""
            SELECT id, log_date, area_id, manager, created_at, updated_at
            FROM daily_logs
            WHERE log_date = :log_date AND area_id = :area_id
        """)

        result = db.session.execute(sql, {
            'log_date': log_date,
            'area_id': user_area.id
        }).fetchone()

        if result:
            # 如果找到日志，重定向到问题记录页面
            return redirect(url_for('daily_management.issues', log_id=result.id))

        # 如果没有找到日志，使用原始 SQL 创建新的日志
        insert_sql = text("""
            INSERT INTO daily_logs
            (log_date, area_id, manager, created_by)
            OUTPUT inserted.id
            VALUES
            (:log_date, :area_id, :manager, :created_by)
        """)

        result = db.session.execute(insert_sql, {
            'log_date': log_date,
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'created_by': current_user.id
        })

        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的问题记录页面
        return redirect(url_for('daily_management.issues', log_id=log_id))
    except Exception as e:
        flash(f'访问问题记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 添加特殊事件API
@daily_management_bp.route('/api/events/add', methods=['GET', 'POST'])
@login_required
def add_event():
    """添加特殊事件API"""
    if request.method == 'GET':
        # 获取日志ID
        log_id = request.args.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的特殊事件'}), 403

        # 返回表单数据
        return jsonify({
            'success': True,
            'log_id': log_id,
            'log_date': log.log_date if isinstance(log.log_date, str) else log.log_date.strftime('%Y-%m-%d'),
            'area_id': log.area_id,
            'area_name': user_area.name
        })

    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的特殊事件'}), 403

        # 使用原始 SQL 创建特殊事件
        sql = text("""
            INSERT INTO special_events
            (daily_log_id, event_type, event_name, event_time, description,
             participants, created_by)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :event_type, :event_name, :event_time, :description,
             :participants, :created_by)
        """)

        result = db.session.execute(sql, {
            'daily_log_id': log_id,
            'event_type': data.get('event_type'),
            'event_name': data.get('event_name'),
            'event_time': datetime.now().replace(microsecond=0),
            'description': data.get('description'),
            'participants': data.get('participants'),
            'created_by': current_user.id
        })

        event_id = result.fetchone()[0]
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'event')

            if file_path:
                # 使用原始 SQL 创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (file_path, reference_type, reference_id, upload_time, uploaded_by)
                    VALUES
                    (:file_path, :reference_type, :reference_id, :upload_time, :uploaded_by)
                """)

                db.session.execute(photo_sql, {
                    'file_path': file_path,
                    'reference_type': 'event',
                    'reference_id': event_id,
                    'upload_time': datetime.now().replace(microsecond=0),
                    'uploaded_by': current_user.id
                })

                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '特殊事件添加成功',
            'event_id': event_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加特殊事件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加特殊事件失败: {str(e)}'}), 500

# 添加问题记录页面
@daily_management_bp.route('/issues/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_issue(log_id):
    """添加问题记录页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限添加该日志的问题记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            issue_type = request.form.get('issue_type')
            issue_description = request.form.get('issue_description')
            severity = request.form.get('severity', 'medium')
            finder = request.form.get('finder') or current_user.real_name or current_user.username
            responsible_person = request.form.get('responsible_person')

            # 验证必填字段
            if not issue_type or not issue_description:
                flash('问题类型和问题描述为必填项', 'danger')
                return render_template('daily_management/add_issue.html',
                                     title='添加问题记录',
                                     log=log,
                                     form=form)

            # 使用原始 SQL 创建问题记录
            sql = text("""
                INSERT INTO issues
                (daily_log_id, issue_type, issue_description, severity, finder,
                 responsible_person, found_time, status, created_by)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :issue_type, :issue_description, :severity, :finder,
                 :responsible_person, :found_time, :status, :created_by)
            """)

            result = db.session.execute(sql, {
                'daily_log_id': log_id,
                'issue_type': issue_type,
                'issue_description': issue_description,
                'severity': severity,
                'finder': finder,
                'responsible_person': responsible_person,
                'found_time': datetime.now().replace(microsecond=0),
                'status': 'pending',
                'created_by': current_user.id
            })

            issue_id = result.fetchone()[0]

            # 处理照片上传
            photo_files = request.files.getlist('photos')
            for photo_file in photo_files:
                if photo_file and photo_file.filename:
                    file_path = handle_photo_upload(photo_file, 'issue')

                    if file_path:
                        # 创建照片记录
                        photo = Photo(
                            file_name=photo_file.filename,
                            file_path=file_path,
                            reference_type='issue',
                            reference_id=issue_id
                        )
                        db.session.add(photo)

            db.session.commit()
            flash('问题记录添加成功', 'success')
            return redirect(url_for('daily_management.issues', log_id=log_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加问题记录失败: {str(e)}")
            flash(f'添加问题记录失败: {str(e)}', 'danger')

    return render_template('daily_management/add_issue.html',
                          title='添加问题记录',
                          log=log,
                          form=form)

# 添加问题记录API
@daily_management_bp.route('/api/issues/add', methods=['POST'])
@login_required
def add_issue_api():
    """添加问题记录API"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的问题记录'}), 403

        # 使用原始 SQL 创建问题记录
        sql = text("""
            INSERT INTO issues
            (daily_log_id, issue_type, issue_name, found_time, description,
             status, created_by)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :issue_type, :issue_name, :found_time, :description,
             :status, :created_by)
        """)

        result = db.session.execute(sql, {
            'daily_log_id': log_id,
            'issue_type': data.get('issue_type'),
            'issue_name': data.get('issue_name'),
            'found_time': datetime.now().replace(microsecond=0),
            'description': data.get('description'),
            'status': data.get('status', 'pending'),
            'created_by': current_user.id
        })

        issue_id = result.fetchone()[0]
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'issue')

            if file_path:
                # 使用原始 SQL 创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (file_path, reference_type, reference_id, upload_time, uploaded_by)
                    VALUES
                    (:file_path, :reference_type, :reference_id, :upload_time, :uploaded_by)
                """)

                db.session.execute(photo_sql, {
                    'file_path': file_path,
                    'reference_type': 'issue',
                    'reference_id': issue_id,
                    'upload_time': datetime.now().replace(microsecond=0),
                    'uploaded_by': current_user.id
                })

                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '问题记录添加成功',
            'issue_id': issue_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加问题记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加问题记录失败: {str(e)}'}), 500

# 更新特殊事件API
@daily_management_bp.route('/api/events/update/<int:event_id>', methods=['POST'])
@login_required
def update_event(event_id):
    """更新特殊事件API"""
    try:
        # 获取特殊事件
        event = SpecialEvent.query.get_or_404(event_id)

        # 获取日志
        log = DailyLog.query.get_or_404(event.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该特殊事件'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 使用原始 SQL 更新特殊事件
        sql = text("""
            UPDATE special_events
            SET event_type = :event_type,
                event_name = :event_name,
                description = :description,
                participants = :participants,
                updated_at = :updated_at
            WHERE id = :event_id
        """)

        db.session.execute(sql, {
            'event_type': data.get('event_type', event.event_type),
            'event_name': data.get('event_name', event.event_name),
            'description': data.get('description', event.description),
            'participants': data.get('participants', event.participants),
            'updated_at': datetime.now().replace(microsecond=0),
            'event_id': event_id
        })

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'event')

            if file_path:
                # 使用原始 SQL 创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (file_path, reference_type, reference_id, upload_time, uploaded_by)
                    VALUES
                    (:file_path, :reference_type, :reference_id, :upload_time, :uploaded_by)
                """)

                db.session.execute(photo_sql, {
                    'file_path': file_path,
                    'reference_type': 'event',
                    'reference_id': event_id,
                    'upload_time': datetime.now().replace(microsecond=0),
                    'uploaded_by': current_user.id
                })

                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '特殊事件更新成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新特殊事件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新特殊事件失败: {str(e)}'}), 500

# 更新问题记录API
@daily_management_bp.route('/api/issues/update/<int:issue_id>', methods=['POST'])
@login_required
def update_issue(issue_id):
    """更新问题记录API"""
    try:
        # 获取问题记录
        issue = Issue.query.get_or_404(issue_id)

        # 获取日志
        log = DailyLog.query.get_or_404(issue.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该问题记录'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 使用原始 SQL 更新问题记录
        sql = text("""
            UPDATE issues
            SET issue_type = :issue_type,
                issue_name = :issue_name,
                description = :description,
                status = :status,
                solution = :solution,
                resolved_time = CASE
                    WHEN :status = 'resolved' AND resolved_time IS NULL
                    THEN :resolved_time
                    ELSE resolved_time
                END,
                resolved_by = CASE
                    WHEN :status = 'resolved' AND resolved_time IS NULL
                    THEN :resolved_by
                    ELSE resolved_by
                END,
                updated_at = :updated_at
            WHERE id = :issue_id
        """)

        db.session.execute(sql, {
            'issue_type': data.get('issue_type', issue.issue_type),
            'issue_name': data.get('issue_name', issue.issue_name),
            'description': data.get('description', issue.description),
            'status': data.get('status', issue.status),
            'solution': data.get('solution', issue.solution),
            'resolved_time': datetime.now().replace(microsecond=0),
            'resolved_by': current_user.id,
            'updated_at': datetime.now().replace(microsecond=0),
            'issue_id': issue_id
        })

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'issue')

            if file_path:
                # 使用原始 SQL 创建照片记录
                photo_sql = text("""
                    INSERT INTO photos
                    (file_path, reference_type, reference_id, upload_time, uploaded_by)
                    VALUES
                    (:file_path, :reference_type, :reference_id, :upload_time, :uploaded_by)
                """)

                db.session.execute(photo_sql, {
                    'file_path': file_path,
                    'reference_type': 'issue',
                    'reference_id': issue_id,
                    'upload_time': datetime.now().replace(microsecond=0),
                    'uploaded_by': current_user.id
                })

                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '问题记录更新成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新问题记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新问题记录失败: {str(e)}'}), 500

# 删除特殊事件API
@daily_management_bp.route('/api/events/delete/<int:event_id>', methods=['POST'])
@login_required
def delete_event(event_id):
    """删除特殊事件API"""
    try:
        # 获取特殊事件
        event = SpecialEvent.query.get_or_404(event_id)

        # 获取日志
        log = DailyLog.query.get_or_404(event.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该特殊事件'}), 403

        # 使用原始 SQL 删除关联的照片
        photo_sql = text("""
            DELETE FROM photos
            WHERE reference_type = 'event' AND reference_id = :event_id
        """)
        db.session.execute(photo_sql, {'event_id': event_id})

        # 使用原始 SQL 删除特殊事件
        event_sql = text("""
            DELETE FROM special_events
            WHERE id = :event_id
        """)
        db.session.execute(event_sql, {'event_id': event_id})

        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '特殊事件删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除特殊事件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除特殊事件失败: {str(e)}'}), 500

# 删除问题记录API
@daily_management_bp.route('/api/issues/delete/<int:issue_id>', methods=['POST'])
@login_required
def delete_issue(issue_id):
    """删除问题记录API"""
    try:
        # 获取问题记录
        issue = Issue.query.get_or_404(issue_id)

        # 获取日志
        log = DailyLog.query.get_or_404(issue.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该问题记录'}), 403

        # 使用原始 SQL 删除关联的照片
        photo_sql = text("""
            DELETE FROM photos
            WHERE reference_type = 'issue' AND reference_id = :issue_id
        """)
        db.session.execute(photo_sql, {'issue_id': issue_id})

        # 使用原始 SQL 删除问题记录
        issue_sql = text("""
            DELETE FROM issues
            WHERE id = :issue_id
        """)
        db.session.execute(issue_sql, {'issue_id': issue_id})

        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '问题记录删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除问题记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除问题记录失败: {str(e)}'}), 500

# 获取特殊事件API
@daily_management_bp.route('/api/events/<int:log_id>', methods=['GET'])
@login_required
def get_events(log_id):
    """获取特殊事件API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的特殊事件'}), 403

        # 使用原始 SQL 获取特殊事件和照片
        sql = text("""
            SELECT e.*, p.id as photo_id, p.file_path, p.rating, p.upload_time
            FROM special_events e
            LEFT JOIN photos p ON p.reference_type = 'event' AND p.reference_id = e.id
            WHERE e.daily_log_id = :log_id
            ORDER BY e.event_time
        """)

        result = db.session.execute(sql, {'log_id': log_id})

        # 处理查询结果
        events = []
        current_event = None

        for row in result:
            if not current_event or current_event['id'] != row.id:
                if current_event:
                    events.append(current_event)

                current_event = {
                    'id': row.id,
                    'event_type': row.event_type,
                    'event_name': row.event_name,
                    'event_time': row.event_time.strftime('%Y-%m-%d %H:%M:%S') if row.event_time and not isinstance(row.event_time, str) else row.event_time,
                    'description': row.description,
                    'participants': row.participants,
                    'created_at': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at and not isinstance(row.created_at, str) else row.created_at,
                    'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M:%S') if row.updated_at and not isinstance(row.updated_at, str) else row.updated_at,
                    'photos': []
                }

            if row.photo_id:
                current_event['photos'].append({
                    'id': row.photo_id,
                    'file_path': row.file_path,
                    'rating': row.rating,
                    'upload_time': row.upload_time.strftime('%Y-%m-%d %H:%M:%S') if row.upload_time and not isinstance(row.upload_time, str) else row.upload_time
                })

        if current_event:
            events.append(current_event)

        # 返回成功响应
        return jsonify({
            'success': True,
            'events': events
        })

    except Exception as e:
        current_app.logger.error(f"获取特殊事件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取特殊事件失败: {str(e)}'}), 500

# 获取问题记录API
@daily_management_bp.route('/api/issues/<int:log_id>', methods=['GET'])
@login_required
def get_issues(log_id):
    """获取问题记录API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的问题记录'}), 403

        # 使用原始 SQL 获取问题记录和照片
        sql = text("""
            SELECT i.*, p.id as photo_id, p.file_path, p.rating, p.upload_time
            FROM issues i
            LEFT JOIN photos p ON p.reference_type = 'issue' AND p.reference_id = i.id
            WHERE i.daily_log_id = :log_id
            ORDER BY i.found_time
        """)

        result = db.session.execute(sql, {'log_id': log_id})

        # 处理查询结果
        issues = []
        current_issue = None

        for row in result:
            if not current_issue or current_issue['id'] != row.id:
                if current_issue:
                    issues.append(current_issue)

                current_issue = {
                    'id': row.id,
                    'issue_type': row.issue_type,
                    'issue_name': row.issue_name,
                    'found_time': row.found_time.strftime('%Y-%m-%d %H:%M:%S') if row.found_time and not isinstance(row.found_time, str) else row.found_time,
                    'description': row.description,
                    'status': row.status,
                    'solution': row.solution,
                    'resolved_time': row.resolved_time.strftime('%Y-%m-%d %H:%M:%S') if row.resolved_time and not isinstance(row.resolved_time, str) else row.resolved_time,
                    'created_at': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at and not isinstance(row.created_at, str) else row.created_at,
                    'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M:%S') if row.updated_at and not isinstance(row.updated_at, str) else row.updated_at,
                    'photos': []
                }

            if row.photo_id:
                current_issue['photos'].append({
                    'id': row.photo_id,
                    'file_path': row.file_path,
                    'rating': row.rating,
                    'upload_time': row.upload_time.strftime('%Y-%m-%d %H:%M:%S') if row.upload_time and not isinstance(row.upload_time, str) else row.upload_time
                })

        if current_issue:
            issues.append(current_issue)

        # 返回成功响应
        return jsonify({
            'success': True,
            'issues': issues
        })

    except Exception as e:
        current_app.logger.error(f"获取问题记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取问题记录失败: {str(e)}'}), 500
