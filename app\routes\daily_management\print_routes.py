"""
打印功能模块
"""
from .common_imports import *

# 打印检查记录
@daily_management_bp.route('/print-inspections/<int:log_id>')
@login_required
def print_inspections(log_id):
    """打印检查记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印检查记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该检查记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 渲染打印模板
    return render_template('daily_management/print/print_inspections.html',
                          title=f'{user_area.name} - 检查记录打印',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          school=user_area)

# 通过日期打印检查记录
@daily_management_bp.route('/print/inspection/<date>')
@login_required
def print_inspection(date):
    """通过日期打印检查记录"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').date()
        log = DailyLog.query.filter_by(log_date=log_date).first_or_404()
        return redirect(url_for('daily_management.print_inspections', log_id=log.id))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印陪餐记录
@daily_management_bp.route('/print-companions/<int:log_id>')
@login_required
def print_companions(log_id):
    """打印陪餐记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印陪餐记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log_id).order_by(DiningCompanion.dining_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_companions.html',
                          title=f'{user_area.name} - 陪餐记录打印',
                          log=log,
                          companions=companions,
                          school=user_area)

# 通过日期打印陪餐记录
@daily_management_bp.route('/print/companion/<date>')
@login_required
def print_companion(date):
    """通过日期打印陪餐记录"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').date()
        log = DailyLog.query.filter_by(log_date=log_date).first_or_404()
        return redirect(url_for('daily_management.print_companions', log_id=log.id))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印单个陪餐记录详情
@daily_management_bp.route('/print-companion-detail/<int:companion_id>')
@login_required
def print_companion_detail(companion_id):
    """打印单个陪餐记录详情"""
    # 获取陪餐记录
    companion = DiningCompanion.query.get_or_404(companion_id)

    # 获取日志
    log = DailyLog.query.get_or_404(companion.daily_log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限打印该陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取照片
    photos = Photo.query.filter_by(reference_type='companion', reference_id=companion_id).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_companion_detail.html',
                          title=f'{user_area.name} - 陪餐记录详情打印',
                          companion=companion,
                          log=log,
                          photos=photos,
                          school=user_area)

# 打印培训记录
@daily_management_bp.route('/print-trainings/<int:log_id>')
@login_required
def print_trainings(log_id):
    """打印培训记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印培训记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).order_by(CanteenTrainingRecord.training_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_trainings.html',
                          title=f'{user_area.name} - 培训记录打印',
                          log=log,
                          trainings=trainings,
                          school=user_area)

# 通过日期打印培训记录
@daily_management_bp.route('/print/training/<date>')
@login_required
def print_training(date):
    """通过日期打印培训记录"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').date()
        log = DailyLog.query.filter_by(log_date=log_date).first_or_404()
        return redirect(url_for('daily_management.print_trainings', log_id=log.id))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印特殊事件
@daily_management_bp.route('/print-events/<int:log_id>')
@login_required
def print_events(log_id):
    """打印特殊事件"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印特殊事件', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该特殊事件', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取特殊事件
    events = SpecialEvent.query.filter_by(daily_log_id=log_id).order_by(SpecialEvent.event_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_events.html',
                          title=f'{user_area.name} - 特殊事件打印',
                          log=log,
                          events=events,
                          school=user_area)

# 通过日期打印特殊事件
@daily_management_bp.route('/print/event/<date>')
@login_required
def print_event(date):
    """通过日期打印特殊事件"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').date()
        log = DailyLog.query.filter_by(log_date=log_date).first_or_404()
        return redirect(url_for('daily_management.print_events', log_id=log.id))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印问题记录
@daily_management_bp.route('/print-issues/<int:log_id>')
@login_required
def print_issues(log_id):
    """打印问题记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印问题记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该问题记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取问题记录
    issues = Issue.query.filter_by(daily_log_id=log_id).order_by(Issue.found_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_issues.html',
                          title=f'{user_area.name} - 问题记录打印',
                          log=log,
                          issues=issues,
                          school=user_area)

# 通过日期打印问题记录
@daily_management_bp.route('/print/issue/<date>')
@login_required
def print_issue(date):
    """通过日期打印问题记录"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').date()
        log = DailyLog.query.filter_by(log_date=log_date).first_or_404()
        return redirect(url_for('daily_management.print_issues', log_id=log.id))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印日志摘要
@daily_management_bp.route('/print-summary/<int:log_id>')
@login_required
def print_summary(log_id):
    """打印日志摘要"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印日志摘要', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该日志摘要', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log_id).order_by(DiningCompanion.dining_time).all()

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).order_by(CanteenTrainingRecord.training_time).all()

    # 获取特殊事件
    events = SpecialEvent.query.filter_by(daily_log_id=log_id).order_by(SpecialEvent.event_time).all()

    # 获取问题记录
    issues = Issue.query.filter_by(daily_log_id=log_id).order_by(Issue.found_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/daily_summary.html',
                          title=f'{user_area.name} - 日志摘要打印',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          companions=companions,
                          trainings=trainings,
                          events=events,
                          issues=issues,
                          school=user_area)

# 通过日期打印日志摘要
@daily_management_bp.route('/print/summary/<date>')
@login_required
def print_summary_by_date(date):
    """通过日期打印日志摘要"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').replace(microsecond=0)
        sql = text("""
            SELECT id FROM daily_logs
            WHERE CONVERT(DATETIME2(1), log_date, 120) = CONVERT(DATETIME2(1), :log_date, 120)
        """)
        result = db.session.execute(sql, {'log_date': log_date}).fetchone()
        if not result:
            flash('未找到指定日期的日志', 'danger')
            return redirect(url_for('daily_management.index'))
        return redirect(url_for('daily_management.print_summary', log_id=result[0]))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 通过日期生成PDF报表
@daily_management_bp.route('/print-daily-summary/<date>')
@login_required
def print_daily_summary(date):
    """通过日期生成PDF报表"""
    try:
        log_date = datetime.strptime(date, '%Y-%m-%d').replace(microsecond=0)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法生成PDF', 'danger')
            return redirect(url_for('main.index'))

        # 查找日志
        sql = text("""
            SELECT id FROM daily_logs
            WHERE CONVERT(DATETIME2(1), log_date, 120) = CONVERT(DATETIME2(1), :log_date, 120)
            AND area_id = :area_id
        """)
        result = db.session.execute(sql, {'log_date': log_date, 'area_id': user_area.id}).fetchone()
        if not result:
            flash('未找到指定日期的日志', 'danger')
            return redirect(url_for('daily_management.index'))

        return redirect(url_for('daily_management.generate_pdf', log_id=result[0]))
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

# 生成日志摘要PDF
@daily_management_bp.route('/generate-pdf/<int:log_id>')
@login_required
def generate_pdf(log_id):
    """生成日志摘要PDF"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法生成PDF', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能生成自己学校的日志PDF
    if log.area_id != user_area.id:
        flash('您没有权限生成该日志的PDF', 'danger')
        return redirect(url_for('daily_management.logs'))

    try:
        # 生成PDF
        pdf_path = generate_daily_summary_pdf(log_id)

        # 返回PDF文件
        return send_file(pdf_path, as_attachment=True, download_name=f'{user_area.name}_{log.log_date.strftime("%Y-%m-%d")}_日志摘要.pdf')
    except Exception as e:
        flash(f'生成PDF失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))